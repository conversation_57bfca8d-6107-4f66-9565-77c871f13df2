# export ARK_API_KEY="YOUR_API_KEY" 查看API KEY 

import os
import base64
import json
import csv
from volcenginesdkarkruntime import Ark
import datetime
import re
import shutil
import markdown
from bs4 import BeautifulSoup
from pypinyin import pinyin, Style
from PIL import Image
import io
import numpy as np
import multiprocessing  # 新增：导入多进程模块
import time  # 新增：用于记录时间
from tqdm import tqdm  # 新增：导入进度条模块

from image_utils import image_to_base64, validate_base64
from yolo_utils import parse_yolo_annotation
from ai_utils import extract_json_from_response

def generate_default_error_json(question_count=1, json_type="answer"):
    """
    生成默认的错误JSON格式
    question_count: 题目数量
    json_type: "answer" 表示答案格式，"boolean" 表示布尔格式
    """
    if json_type == "boolean":
        # 对于test2.py, test3.py, one_stage_test.py，返回false的布尔值
        return json.dumps({f"题目{i}": False for i in range(1, question_count + 1)}, ensure_ascii=False)
    else:
        # 对于test.py，返回"API请求失败"的答案格式
        return json.dumps({f"题目 {i}": "API请求失败" for i in range(1, question_count + 1)}, ensure_ascii=False)

def extract_question_count_from_json(json_str):
    """
    从JSON字符串中提取题目数量
    """
    if not json_str:
        return 1
    try:
        data = json.loads(json_str)
        if isinstance(data, dict):
            return len(data)
    except:
        pass
    return 1

def process_single_image_local(task):
    """
    处理单张图片的本地操作（增强、缩放、base64编码）
    """
    image_path = task['image_path']
    img_filename = task['img_filename']
    use_enhance = task['use_enhance']
    enhance_threshold = task['enhance_threshold']
    scale = task['scale']
    use_pixel_connection = task['use_pixel_connection']

    try:
        base64_image = image_to_base64(image_path, use_enhance=use_enhance,
                                     enhance_threshold=enhance_threshold,
                                     scale=scale, use_pixel_connection=use_pixel_connection)
        return {
            'success': True,
            'img_filename': img_filename,
            'base64_image': base64_image,
            'image_path': image_path
        }
    except Exception as e:
        return {
            'success': False,
            'img_filename': img_filename,
            'base64_image': None,
            'image_path': image_path,
            'error': str(e)
        }

def get_image_files(images_dir):
    """获取images文件夹中的所有图片文件，按文件名字典序排序"""
    image_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp']
    image_files = []
    if os.path.exists(images_dir):
        for filename in os.listdir(images_dir):
            if any(filename.lower().endswith(ext) for ext in image_extensions):
                image_files.append(os.path.join(images_dir, filename))
    # 按文件名字典序排序
    return sorted(image_files)

def markdown_to_text(markdown_content):
    """将markdown格式转换为纯文本"""
    # 将markdown转换为HTML
    html = markdown.markdown(markdown_content)
    # 使用BeautifulSoup提取纯文本
    soup = BeautifulSoup(html, 'html.parser')
    # 获取纯文本内容
    text = soup.get_text()
    # 清理多余的空白字符
    text = re.sub(r'\n\s*\n', '\n\n', text)  # 将多个空行替换为两个换行
    text = re.sub(r'[ \t]+', ' ', text)  # 将多个空格替换为单个空格
    return text.strip()

def chinese_to_pinyin(chinese_text):
    """将中文转换为拼音（无声调）"""
    pinyin_list = pinyin(chinese_text, style=Style.NORMAL)
    return ''.join([p[0] for p in pinyin_list])

def get_latest_folder(base_dir, prefix="images_"):
    """获取指定目录下以prefix开头的最新文件夹"""
    if not os.path.exists(base_dir):
        return None

    folders = []
    for item in os.listdir(base_dir):
        item_path = os.path.join(base_dir, item)
        if os.path.isdir(item_path) and item.startswith(prefix):
            folders.append(item)

    if not folders:
        return None

    # 按文件夹名排序，取最新的（字典序最大的）
    folders.sort()
    return folders[-1]

def get_folder_choice(question_dir):
    """让用户选择图像文件夹"""
    folder_options = {
        "1": "images",
        "2": "OpenCV_result",
        "3": "grounding_result",
        "4": "YOLO_result",
        "5": "YOLO_text_result",
        "6": "manual_result",
        "7": "roboflow_yolo_result"
    }

    print("\n请选择图像文件夹：")
    for key, value in folder_options.items():
        print(f"{key}. {value}")

    while True:
        user_input = input("请输入文件夹编号（1-7）：").strip()

        if user_input in folder_options:
            folder_name = folder_options[user_input]
            break
        else:
            print("输入无效，请输入 1-7 的数字")

    # 如果选择的是结果文件夹，需要找到最新的子文件夹
    if folder_name != "images":
        result_dir = os.path.join(question_dir, folder_name)
        latest_folder = get_latest_folder(result_dir)
        if latest_folder:
            actual_images_dir = os.path.join(result_dir, latest_folder)
            print(f"选择的文件夹：{folder_name}")
            print(f"最新的子文件夹：{latest_folder}")
            print(f"实际图片路径：{actual_images_dir}")
            # 返回相对于question_dir的路径，用于生成正确的markdown图片路径
            relative_path = os.path.join(folder_name, latest_folder).replace("\\", "/")
            return actual_images_dir, relative_path
        else:
            print(f"错误：{folder_name} 文件夹下没有找到任何图片文件夹！")
            return None, None
    else:
        images_dir = os.path.join(question_dir, "images")
        print(f"选择的文件夹：images")
        print(f"实际图片路径：{images_dir}")
        return images_dir, "images"

def get_model_choice():
    """让用户选择模型ID"""
    available_models = {
        "1": "doubao-seed-1-6-250615",
        "2": "doubao-seed-1-6-flash-250715",
        "3": "doubao-1-5-thinking-vision-pro-250428",
        "4": "doubao-1-5-vision-pro-32k-250115"
    }

    print("请选择模型ID：")
    for key, value in available_models.items():
        print(f"{key}. {value}")

    while True:
        user_input = input("请输入模型编号（1-4）：").strip()
        if user_input in available_models:
            selected_model = available_models[user_input]
            print(f"选择的模型：{selected_model}")
            return selected_model
        else:
            print("输入无效，请输入 1-4 的数字")


def get_response_format_choice(model_id):
    """根据模型ID判断是否支持jsonObject，如果支持则让用户选择response_format"""
    # 支持jsonObject的模型列表（模型1、2、3）
    json_object_supported_models = [
        "doubao-seed-1-6-250615",
        "doubao-seed-1-6-flash-250715",
        "doubao-1-5-thinking-vision-pro-250428"
    ]

    if model_id in json_object_supported_models:
        print("选择response_format：")
        print("1. text")
        print("2. json_object")

        while True:
            user_input = input("请输入选择（1-2）：").strip()
            if user_input == "1":
                return "text"
            elif user_input == "2":
                return "json_object"
            else:
                print("输入无效，请输入 1 或 2")
    else:
        return "text"  # 不支持jsonObject的模型默认使用text

def get_max_tokens_for_model(model_id):
    """根据模型ID返回对应的max_tokens值"""
    if model_id == "doubao-1-5-vision-pro-32k-250115":  # 模型4
        return 12288  # 12K
    else:  # 模型1、2、3
        return 16384  # 16K

def load_answer_relationship(csv_path):
    """从CSV文件中加载图片和答案图片的对应关系"""
    relationship = {}
    if not os.path.exists(csv_path):
        print(f"警告：答案关系文件 {csv_path} 不存在！")
        return relationship

    try:
        with open(csv_path, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row in reader:
                relationship[row['images']] = row['answer_images']
        print(f"已从 {csv_path} 加载 {len(relationship)} 个图片答案关系")
    except Exception as e:
        print(f"读取答案关系文件时出错：{str(e)}")

    return relationship

def process_single_image_two_images_api(task):
    """
    双图片API推理，输入为task字典（包含题目图片base64、答案图片base64、图片名、two_images_prompt等）
    """
    from volcenginesdkarkruntime import Ark
    import os
    img_filename = task['img_filename']
    base64_image = task['base64_image']
    answer_base64_image = task['answer_base64_image']
    two_images_prompt = task['two_images_prompt']
    client_api_key = task['client_api_key']
    model_id = task.get('model_id', 'doubao-seed-1-6-flash-250715')  # 支持模型参数
    response_format = task.get('response_format', 'text')  # 获取response_format参数
    index = task['index']
    image_path_prefix = task['image_path_prefix']
    # 获取API参数，如果没有传入则使用默认值
    temperature = task.get('temperature', 1)
    top_p = task.get('top_p', 0.7)
    max_tokens_param = task.get('max_tokens')  # 如果没有传入则为None，后面会使用默认逻辑
    sep = f"\n{'=' * 50}\n"
    info = f"处理第 {index} 张图片: {img_filename}"
    current_image_output_lines = []
    current_image_output_lines.append(sep)
    current_image_output_lines.append(info + "\n")
    current_image_output_lines.append(sep)
    current_image_output_lines.append(f"![{img_filename}]({image_path_prefix}{img_filename})\n")

    # 重试机制：最多重试2次，只对Connection error进行重试
    max_retries = 2
    for attempt in range(max_retries + 1):
        try:
            client_local = Ark(
                base_url="https://ark.cn-beijing.volces.com/api/v3",
                api_key=client_api_key,
            )
            # 记录开始时间
            start_time = time.time()

            # 构建请求内容，包含题目图片、答案图片和two_images_prompt
            content = [
                {"type": "image_url", "image_url": {"url": base64_image, "detail": "high"}},
                {"type": "text", "text": two_images_prompt},
                {"type": "image_url", "image_url": {"url": answer_base64_image, "detail": "high"}}
            ]

            # 构建未加密的显示版本内容（省略base64图片内容）
            content_display = []
            for item in content:
                if item.get("type") == "image_url":
                    # 省略base64图片内容
                    url = item["image_url"].get("url", "")
                    if url.startswith("data:image/") and "," in url:
                        prefix, base64_data = url.split(",", 1)
                        if len(base64_data) > 10:
                            display_url = f"{prefix},{base64_data[:10]}...[省略{len(base64_data)-10}个字符]"
                        else:
                            display_url = url
                    else:
                        display_url = url
                    content_display.append({
                        "type": "image_url",
                        "image_url": {"url": display_url, "detail": "high"}
                    })
                else:
                    # 对于text类型，直接使用原始内容（未加密）
                    content_display.append(item.copy())

            # 构建请求参数（先保存未加密版本用于显示）
            request_params_display = {
                "model": model_id,
                "messages": [
                    {
                        "role": "user",
                        "content": content_display
                    }
                ],
                "max_tokens": max_tokens_param if max_tokens_param is not None else get_max_tokens_for_model(model_id),
                "thinking": {"type": "disabled"}
            }

            # 只有当temperature不为None时才添加到请求体中
            if temperature is not None:
                request_params_display["temperature"] = temperature

            # 只有当top_p不为None时才添加到请求体中
            if top_p is not None:
                request_params_display["top_p"] = top_p

            # 构建实际请求参数（包含加密头）
            request_params = {
                "model": model_id,
                "messages": [
                    {
                        "role": "user",
                        "content": content
                    }
                ],
                "extra_headers": {'x-is-encrypted': 'true'},
                "max_tokens": max_tokens_param if max_tokens_param is not None else get_max_tokens_for_model(model_id),
                "thinking": {"type": "disabled"}
            }

            # 只有当temperature不为None时才添加到请求体中
            if temperature is not None:
                request_params["temperature"] = temperature

            # 只有当top_p不为None时才添加到请求体中
            if top_p is not None:
                request_params["top_p"] = top_p

            # 如果选择了json_object格式，添加response_format参数
            if response_format == "json_object":
                request_params["response_format"] = {"type": "json_object"}
                request_params_display["response_format"] = {"type": "json_object"}

            response = client_local.chat.completions.create(**request_params)
            # 记录结束时间并计算响应时间
            end_time = time.time()
            response_time = end_time - start_time
            resp_content = response.choices[0].message.content.strip()
            if resp_content.startswith("```json"):
                resp_content = resp_content[7:]
            if resp_content.startswith("```"):
                resp_content = resp_content[3:]
            if resp_content.endswith("```"):
                resp_content = resp_content[:-3]
            resp_content = resp_content.strip()

            # 如果是重试成功，添加重试信息
            if attempt > 0:
                current_image_output_lines.append(f"### 重试信息：第 {attempt + 1} 次尝试成功\n")

            # 添加正确答案图片信息
            current_image_output_lines.append(f"### 正确答案图片：\n")
            current_image_output_lines.append(f"![答案图片](../two_images_answer/{task['answer_img_filename']})\n\n")

            current_image_output_lines.append(f"### 响应内容：\n")
            current_image_output_lines.append("```json\n")
            current_image_output_lines.append(f"{resp_content}\n")
            current_image_output_lines.append("```\n")

            # 添加请求体（使用未加密版本，已经省略了base64图片内容）
            current_image_output_lines.append(f"### 请求体：\n")
            current_image_output_lines.append("```json\n")
            current_image_output_lines.append(f"{json.dumps(request_params_display, ensure_ascii=False, indent=2)}\n")
            current_image_output_lines.append("```\n")

            # 添加响应时间记录
            current_image_output_lines.append(f"### 响应时间：{response_time:.2f}秒\n")
            # token用量信息
            usage = getattr(response, 'usage', None)
            total_tokens = usage.total_tokens if usage and hasattr(usage, 'total_tokens') else None
            cached_tokens = None
            reasoning_tokens = None
            if usage:
                if hasattr(usage, 'prompt_tokens_details') and usage.prompt_tokens_details:
                    cached_tokens = getattr(usage.prompt_tokens_details, 'cached_tokens', None)
                if hasattr(usage, 'completion_tokens_details') and usage.completion_tokens_details:
                    reasoning_tokens = getattr(usage.completion_tokens_details, 'reasoning_tokens', None)
            current_image_output_lines.append("### token用量\n")
            current_image_output_lines.append(f"- total_tokens: {total_tokens}\n")
            current_image_output_lines.append(f"- cached_tokens: {cached_tokens}\n")
            current_image_output_lines.append(f"- reasoning_tokens: {reasoning_tokens}\n")
            return {
                'success': True,
                'image_path': task['image_path'],
                'output_lines': current_image_output_lines,
                'response_content': resp_content
            }
        except Exception as e:
            error_str = str(e)
            # 检查是否为Connection error，如果是且还有重试机会，则继续重试
            if "Connection error" in error_str and attempt < max_retries:
                print(f"[PID {os.getpid()}] 处理图片 {img_filename} 时出现连接错误，正在重试... (第 {attempt + 1} 次尝试)")
                time.sleep(1)  # 等待1秒后重试
                continue

            # 如果不是Connection error或已达到最大重试次数，则返回失败
            err_msg = f"处理图片 {img_filename} 时出错: {error_str}"
            if attempt > 0:
                err_msg += f" (已重试 {attempt} 次)"
            # 只在失败时打印错误信息
            print(f"[PID {os.getpid()}] {err_msg}")
            current_image_output_lines.append(err_msg + "\n")

            # 生成简单的错误JSON格式
            error_json = json.dumps({"请求异常": error_str}, ensure_ascii=False)

            # 添加正确答案图片信息
            current_image_output_lines.append(f"### 正确答案图片：\n")
            current_image_output_lines.append(f"![答案图片](../two_images_answer/{task['answer_img_filename']})\n\n")

            current_image_output_lines.append(f"### 响应内容：\n")
            current_image_output_lines.append("```json\n")
            current_image_output_lines.append(f"{error_json}\n")
            current_image_output_lines.append("```\n")

            return {
                'success': False,
                'image_path': task['image_path'],
                'output_lines': current_image_output_lines,
                'response_content': error_json
            }

def run_two_images_test(model_id=None, response_format=None, images_dir=None, use_enhance=None, scale=None, use_pixel_connection=None, custom_prompt=None, temperature=None, top_p=None, max_tokens=None, gray_threshold=None):
    """
    运行双图片测试，专门针对画图题
    """
    # 请确保您已将 API Key 存储在环境变量 ARK_API_KEY 中
    # 初始化Ark客户端，从环境变量中读取您的API Key
    client = Ark(
        # 此为默认路径，您可根据业务所在地域进行配置
        base_url="https://ark.cn-beijing.volces.com/api/v3",
        # 从环境变量中获取您的 API Key。此为默认方式，您可根据需要进行修改
        api_key="36c2aa0e-8b2b-4412-bc92-d3d1cef96b1b",
    )

    # 如果没有传入模型ID，则获取用户选择的模型
    if model_id is None:
        model_id = get_model_choice()

    # 如果没有传入response_format，则获取用户选择的response_format
    if response_format is None:
        response_format = get_response_format_choice(model_id)

    # 固定使用画图题
    question_type = "画图题"
    pinyin_name = "huatuti"

    print(f"使用模型: {model_id}")
    print(f"题型: {question_type}")

    # 构建题型相关的路径
    types_dir = "types"
    question_dir = os.path.join(types_dir, pinyin_name)

    # 让用户选择图像文件夹
    if images_dir is None:
        images_dir, image_path_prefix = get_folder_choice(question_dir)
        if images_dir is None:
            print("无法获取图像文件夹，程序退出")
            exit()
    else:
        images_dir = os.path.join(question_dir, images_dir)
        image_path_prefix = images_dir.replace(question_dir, "").replace("\\", "/")
        print(f"使用外部传入的图片文件夹：{images_dir}")
        print(f"实际图片路径前缀：{image_path_prefix}")

    # 创建two_images_response文件夹
    two_images_response_dir = os.path.join(question_dir, "two_images_response")
    os.makedirs(two_images_response_dir, exist_ok=True)

    # 创建two_images_error文件夹
    two_images_error_dir = os.path.join(question_dir, "two_images_error")
    os.makedirs(two_images_error_dir, exist_ok=True)

    # 文件路径
    two_images_prompt_file = os.path.join(question_dir, "two_images_prompt.md")
    answer_relationship_file = os.path.join(question_dir, "answer_relationship.csv")
    two_images_answer_dir = os.path.join(question_dir, "two_images_answer")

    # 如果没有传入use_enhance参数，则询问用户
    if use_enhance is None:
        while True:
            enhance_input = input("是否采用'灰度阀门与像素增强'处理？(y/n)：").strip().lower()
            if enhance_input in ("y", "n"):
                use_enhance = (enhance_input == "y")
                break
            else:
                print("请输入 y 或 n")

    # 如果选择了灰度阀门与像素增强，询问是否采用黑色像素粘连
    if use_pixel_connection is None:
        use_pixel_connection = False
        if use_enhance:
            while True:
                connection_input = input("是否采用'黑色像素粘连'处理？(y/n)：").strip().lower()
                if connection_input in ("y", "n"):
                    use_pixel_connection = (connection_input == "y")
                    break
                else:
                    print("请输入 y 或 n")

    # 如果没有传入scale参数，则询问用户
    if scale is None:
        while True:
            try:
                scale_input = input("请输入图片放大倍数（如2、4、6、8）：").strip()
                scale = float(scale_input)
                if scale <= 0:
                    print("放大倍数必须为正数！")
                    continue
                break
            except Exception:
                print("请输入有效的数字倍数！")

    # 如果没有传入use_pixel_connection参数，设置默认值
    if use_pixel_connection is None:
        use_pixel_connection = False

    # 处理灰度阀门参数
    if gray_threshold is None:
        gray_threshold = 200  # 使用默认值200
    else:
        # 验证灰度阀门值是否在有效范围内
        if not isinstance(gray_threshold, (int, float)) or not (0 <= gray_threshold <= 255):
            print(f"警告：灰度阀门值 {gray_threshold} 无效，必须在0-255范围内，将使用默认值200")
            gray_threshold = 200

    print(f"\n使用路径：")
    print(f"图片文件夹：{images_dir}")
    print(f"two_images_response文件夹：{two_images_response_dir}")
    print(f"two_images_prompt文件：{two_images_prompt_file}")
    print(f"答案关系文件：{answer_relationship_file}")
    print(f"two_images_error文件夹：{two_images_error_dir}")
    print(f"正确答案图片文件夹：{two_images_answer_dir}")

    # 检查并创建必要的目录
    os.makedirs(images_dir, exist_ok=True)
    os.makedirs(two_images_response_dir, exist_ok=True)
    os.makedirs(two_images_answer_dir, exist_ok=True)

    # 检查必要文件是否存在
    if not custom_prompt and not os.path.exists(two_images_prompt_file):
        print(f"错误：two_images_prompt文件 {two_images_prompt_file} 不存在！")
        print(f"请创建 {two_images_prompt_file} 文件并写入提示词内容")
        exit()

    if not os.path.exists(answer_relationship_file):
        print(f"错误：答案关系文件 {answer_relationship_file} 不存在！")
        exit()

    # 优先使用自定义prompt，否则从two_images_prompt.md文件读取提示词
    if custom_prompt:
        two_images_prompt = custom_prompt
        print("使用从main脚本传递的自定义提示词")
    else:
        try:
            with open(two_images_prompt_file, 'r', encoding='utf-8') as f:
                two_images_markdown_prompt = f.read().strip()
            print(f"已从文件 {two_images_prompt_file} 读取two_images_prompt")
            # 将markdown格式转换为纯文本
            two_images_prompt = markdown_to_text(two_images_markdown_prompt)
            print("已将markdown格式转换为纯文本")
        except Exception as e:
            print(f"读取two_images_prompt文件时出错：{str(e)}")
            exit()

        if not two_images_prompt:
            print("错误：two_images_prompt文件为空！")
            exit()

    # 加载答案关系
    print("正在加载答案关系...")
    answer_relationship = load_answer_relationship(answer_relationship_file)

    # 获取images文件夹中的所有图片文件
    image_files = get_image_files(images_dir)

    if not image_files:
        msg1 = "images文件夹中没有找到图片文件！"
        msg2 = "支持的格式：.jpg, .jpeg, .png, .gif, .webp"
        print(msg1)
        print(msg2)
        return False, {"accuracy": 0.0, "accuracy_str": "无图片文件", "total": 0, "wrong_count": 0, "accuracy_percentage": "0%"}

    print(f"找到 {len(image_files)} 张图片，开始逐个处理...")
    print(f"使用的two_images_prompt: {two_images_prompt}")

    # 生成 two_images_response 文件夹和文件名
    now = datetime.datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
    response_file = os.path.join(two_images_response_dir, f"{now}.md")
    output_lines = []

    # 新增：如果 use_enhance 为 True，最顶部插入说明
    if use_enhance:
        if use_pixel_connection:
            output_lines.append("使用'灰度阀门与像素增强'处理（含黑色像素粘连）\n\n")
        else:
            output_lines.append("使用'灰度阀门与像素增强'处理（不含黑色像素粘连）\n\n")

    # 预留准确率位置（稍后会在文件开头插入）
    output_lines.append(f"# 运行时间: {now}\n\n")
    output_lines.append(f"## 使用模型ID: {model_id}\n\n")
    output_lines.append(f"## 使用图片文件夹: {image_path_prefix}\n\n")
    output_lines.append(f"## 图片放大倍数: {scale}\n\n")

    # 添加使用的提示词
    output_lines.append(f"## 使用的two_images_prompt\n\n")
    output_lines.append(f"{two_images_prompt}\n\n")

    print("\n--- 开始本地处理图片（增强/缩放/编码） ---\n")
    api_key_from_client_init = "36c2aa0e-8b2b-4412-bc92-d3d1cef96b1b"

    # 准备本地处理任务
    local_tasks = []
    for i, image_path in enumerate(image_files):
        img_filename = os.path.basename(image_path)
        local_tasks.append({
            'image_path': image_path,
            'img_filename': img_filename,
            'use_enhance': use_enhance,
            'enhance_threshold': gray_threshold,
            'scale': scale,
            'use_pixel_connection': use_pixel_connection
        })

    # 使用多进程进行本地图片处理
    num_processes = os.cpu_count() if os.cpu_count() else 4
    print(f"正在使用 {num_processes} 个进程进行本地图片处理...")

    local_results = []
    with multiprocessing.Pool(processes=num_processes) as pool:
        # 使用imap代替map以支持进度条
        for result in tqdm(pool.imap(process_single_image_local, local_tasks),
                         total=len(local_tasks), desc="本地处理", unit="张"):
            local_results.append(result)

    # 准备API推理任务，只包含成功处理的图片
    api_tasks = []
    for i, local_result in enumerate(local_results):
        if local_result['success']:
            img_filename = local_result['img_filename']

            # 从答案关系中获取对应的答案图片
            answer_img_filename = answer_relationship.get(img_filename)
            if not answer_img_filename:
                print(f"警告：图片 {img_filename} 在答案关系文件中未找到对应的答案图片，跳过处理")
                continue

            # 检查答案图片是否存在
            answer_img_path = os.path.join(two_images_answer_dir, answer_img_filename)
            if not os.path.exists(answer_img_path):
                print(f"警告：答案图片 {answer_img_path} 不存在，跳过处理")
                continue

            # 处理答案图片为base64
            try:
                answer_base64_image = image_to_base64(answer_img_path, use_enhance=use_enhance,
                                                    enhance_threshold=gray_threshold,
                                                    scale=scale, use_pixel_connection=use_pixel_connection)
            except Exception as e:
                print(f"警告：处理答案图片 {answer_img_path} 时出错：{str(e)}，跳过处理")
                continue

            api_tasks.append({
                'img_filename': local_result['img_filename'],
                'base64_image': local_result['base64_image'],
                'answer_base64_image': answer_base64_image,
                'answer_img_filename': answer_img_filename,
                'two_images_prompt': two_images_prompt,
                'client_api_key': api_key_from_client_init,
                'model_id': model_id,  # 添加模型ID参数
                'response_format': response_format,  # 添加response_format参数
                'index': i + 1,
                'image_path_prefix': f"../{image_path_prefix}/",
                'image_path': local_result['image_path'],
                'temperature': temperature,  # 添加temperature参数
                'top_p': top_p,  # 添加top_p参数
                'max_tokens': max_tokens  # 添加max_tokens参数
            })
        else:
            print(f"跳过处理失败的图片: {local_result['img_filename']}")

    print(f"本地处理完成: {len(api_tasks)}/{len(local_tasks)} 张图片成功处理")
    print(f"\n--- 开始并行API推理 ---\n")
    print(f"将使用 {num_processes} 个进程进行并行API推理。")

    # 使用进度条显示API推理进度
    all_processed_results = []
    with multiprocessing.Pool(processes=num_processes) as pool:
        # 使用imap代替map以支持进度条
        for result in tqdm(pool.imap(process_single_image_two_images_api, api_tasks),
                         total=len(api_tasks), desc="API推理", unit="张"):
            all_processed_results.append(result)

    print("\n--- 并行API推理完成，合并结果 ---\n")
    for result in all_processed_results:
        output_lines.extend(result['output_lines'])

    sep = f"\n{'=' * 50}\n"
    output_lines.append(sep)
    output_lines.append("所有图片处理完成！\n")
    output_lines.append(sep)
    print(sep)
    print("所有图片处理完成！")
    print(sep)

    # 写入新内容到新文件
    with open(response_file, "w", encoding="utf-8") as f:
        f.writelines(output_lines)

    print(f"结果已保存到：{response_file}")

    # 返回成功状态和基本信息
    return True, {
        'accuracy': 1.0,  # 双图片测试没有模板比对，默认返回100%
        'accuracy_str': f'双图片测试完成，处理了 {len(all_processed_results)} 张图片',
        'total': len(all_processed_results),
        'wrong_count': 0,
        'accuracy_percentage': '100%'
    }

if __name__ == "__main__":
    # 如果直接运行此脚本，则使用交互模式
    run_two_images_test()
